/**
 * Webhook Trigger Handler
 *
 * Processes the oldest pending webhook from the webhook_queue table.
 * This handler implements a sequential webhook processing system that:
 * 1. Queries for the oldest pending webhook
 * 2. Updates its status to "processing"
 * 3. Determines the appropriate internal webhook URL based on source
 * 4. Sends HTTP POST request to the internal webhook endpoint
 * 5. Updates webhook status based on response (completed/failed)
 *
 * @fileoverview Webhook trigger handler for sequential processing
 * @version 1.0.0
 * @since 2024-08-05
 */

import { dbSchema, getDb } from "@database";
import type { Context } from "hono";
import { asc, eq } from "drizzle-orm";
import { logError, logInfo } from "@/utils/logger";
import { logWebhookError } from "@/utils/errorLogger";

/**
 * Webhook trigger handler
 *
 * Processes the oldest pending webhook from the queue by:
 * - Finding the oldest pending webhook (status = "pending", ordered by createdAt ASC)
 * - Updating its status to "processing"
 * - Determining the appropriate internal webhook URL based on source
 * - Making HTTP POST request to internal webhook URL with payload
 * - Updating status based on response (completed/failed)
 *
 * @param c - Hono context object
 * @returns Promise resolving to HTTP Response
 */
export async function webhookTriggerHandler(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();

	try {
		logInfo("Webhook trigger endpoint called");

		const db = getDb();

		// Step 1: Find the oldest pending webhook
		const pendingWebhooks = await db
			.select()
			.from(dbSchema.webhookQueue)
			.where(eq(dbSchema.webhookQueue.status, "pending"))
			.orderBy(asc(dbSchema.webhookQueue.createdAt))
			.limit(1);

		if (pendingWebhooks.length === 0) {
			logInfo("No pending webhooks found in queue");
			return c.json(
				{
					status: "error",
					message: "No pending webhooks found",
					metadata: {
						timestamp,
					},
				},
				404,
			);
		}

		const webhook = pendingWebhooks[0];
		logInfo(`Processing webhook: ${webhook.id} (source: ${webhook.source})`);

		// Step 2: Update webhook status to "processing"
		const processingStartedAt = new Date();
		await db
			.update(dbSchema.webhookQueue)
			.set({
				status: "processing",
				processingStartedAt,
				updatedAt: processingStartedAt,
			})
			.where(eq(dbSchema.webhookQueue.id, webhook.id));

		// Step 3: Determine internal webhook URL based on source
		const baseUrl = new URL(c.req.url).origin;
		let internalWebhookUrl: string;

		switch (webhook.source) {
			case "cc":
				internalWebhookUrl = `${baseUrl}/internal-webhook/cc`;
				break;
			case "ap":
				internalWebhookUrl = `${baseUrl}/internal-webhook/ap`;
				break;
			default:
				throw new Error(`Unknown webhook source: ${webhook.source}`);
		}

		logInfo(`Sending webhook to internal endpoint: ${internalWebhookUrl}`);

		// Step 4: Send HTTP POST request to internal webhook URL
		let response: Response;
		let processingResult: "completed" | "failed";
		let errorMessage: string | null = null;
		let errorDetails: Record<string, unknown> | null = null;

		try {
			response = await fetch(internalWebhookUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					"User-Agent": "WebhookTrigger/1.0",
				},
				body: JSON.stringify(webhook.payload),
			});

			if (response.ok) {
				processingResult = "completed";
				logInfo(
					`Webhook ${webhook.id} processed successfully (${response.status})`,
				);
			} else {
				processingResult = "failed";
				const errorText = await response.text();
				errorMessage = `HTTP ${response.status}: ${response.statusText}`;
				errorDetails = {
					status: response.status,
					statusText: response.statusText,
					responseBody: errorText,
				};
				logError(
					`Webhook ${webhook.id} processing failed: ${errorMessage}`,
				);
			}
		} catch (fetchError) {
			processingResult = "failed";
			errorMessage = `Network error: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`;
			errorDetails = {
				error: fetchError instanceof Error ? fetchError.stack : String(fetchError),
				url: internalWebhookUrl,
			};
			logError(`Webhook ${webhook.id} network error: ${errorMessage}`);
		}

		// Step 5: Update webhook status based on response
		const processingCompletedAt = new Date();
		const updateData: Record<string, unknown> = {
			status: processingResult,
			processingCompletedAt,
			updatedAt: processingCompletedAt,
		};

		if (processingResult === "failed") {
			updateData.errorMessage = errorMessage;
			updateData.errorDetails = errorDetails;
		}

		await db
			.update(dbSchema.webhookQueue)
			.set(updateData)
			.where(eq(dbSchema.webhookQueue.id, webhook.id));

		// Log webhook error if processing failed
		if (processingResult === "failed") {
			await logWebhookError(
				new Error(errorMessage || "Unknown webhook processing error"),
				{
					webhookId: webhook.id,
					source: webhook.source,
					sourceId: webhook.sourceId,
					internalUrl: internalWebhookUrl,
					errorDetails,
				},
			);
		}

		// Step 6: Return appropriate response
		const processingDuration = processingCompletedAt.getTime() - processingStartedAt.getTime();

		if (processingResult === "completed") {
			return c.json(
				{
					status: "success",
					message: "Webhook processed successfully",
					metadata: {
						timestamp,
						webhookId: webhook.id,
						source: webhook.source,
						sourceId: webhook.sourceId,
						processingDurationMs: processingDuration,
					},
				},
				200,
			);
		} else {
			return c.json(
				{
					status: "error",
					message: "Webhook processing failed",
					metadata: {
						timestamp,
						webhookId: webhook.id,
						source: webhook.source,
						sourceId: webhook.sourceId,
						processingDurationMs: processingDuration,
						errorMessage,
					},
				},
				500,
			);
		}
	} catch (error) {
		logError("Webhook trigger handler error", error);

		await logWebhookError(
			error instanceof Error ? error : new Error(String(error)),
			{
				endpoint: "/webhook-trigger",
				timestamp,
			},
		);

		return c.json(
			{
				status: "error",
				message: "Internal server error during webhook processing",
				metadata: {
					timestamp,
				},
			},
			500,
		);
	}
}
